import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class BuildItemRecentPlaceWidget extends StatelessWidget {
  final RecentPlaceType type;
  const BuildItemRecentPlaceWidget({
    super.key,
    this.type = RecentPlaceType.place,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: context.paddingMedium,
      decoration: BoxDecoration(
        borderRadius: context.borderRadiusMedium,
        border: Border.all(
          color: AppColors.gray400,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(context.valuePaddingNormal),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.honoluluBlue.withValues(alpha: 0.1),
                ),
                child: SvgPicture.asset(
                  type == RecentPlaceType.place
                      ? Assets.svgs.location.path
                      : Assets.svgs.history.path,
                  width: 24.dm,
                  height: 24.dm,
                  colorFilter: ColorFilter.mode(
                    AppColors.honoluluBlue,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              context.horizontalSpaceNormal,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Hà Nội",
                    style: AppTextStyle.medium(),
                  ),
                  context.verticalSpaceNormal,
                  Text(
                    "Địa chỉ chi tiết",
                    style: AppTextStyle.regular(12.sp).copyWith(
                      color: AppColors.philippineGray,
                    ),
                  ),
                ],
              )
            ],
          ),
          Text(
            "36 km",
            style: AppTextStyle.regular().copyWith(
              color: AppColors.philippineGray,
            ),
          )
        ],
      ),
    );
  }
}
