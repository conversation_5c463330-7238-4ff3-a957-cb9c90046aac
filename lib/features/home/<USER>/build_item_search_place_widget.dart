import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildItemSearchPlaceWidget extends StatelessWidget {
  final PlaceReponse place;
  final Function(PlaceReponse)? callBack;
  const BuildItemSearchPlaceWidget({
    super.key,
   
    this.callBack,
    required this.place,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      onTap: () {
        callBack?.call(place);
        SafeNavigationUtil.pop(context);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Assets.svgs.location.svg(
            width: 24.dm,
            height: 24.dm,
            colorFilter: ColorFilter.mode(
              AppColors.honoluluBlue,
              BlendMode.srcIn,
            ),
          ),
          context.horizontalSpaceNormal,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  place.name ?? '',
                  style: AppTextStyle.semibold(),
                ),
                context.verticalSpaceNormal,
                Text(
                  place.address ?? '',
                  style: AppTextStyle.regular().copyWith(
                    color: AppColors.gray600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
