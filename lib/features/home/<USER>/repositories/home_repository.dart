import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/features/home/<USER>/datasource/home_remote.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:injectable/injectable.dart';

@LazySingleton()
class HomeRepository implements IHomeRepository {
  final HomeRemote remoteDataSource;

  HomeRepository(this.remoteDataSource);

  @override
  FutureEither<AppResponse<List<PlaceReponse>>> getPlaces({
    required String place,
  }) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.getPlaces(place: place),
    );
  } 
}


abstract class IHomeRepository {
  FutureEither<AppResponse<List<PlaceReponse>>> getPlaces({
    required String place,
  });
}
