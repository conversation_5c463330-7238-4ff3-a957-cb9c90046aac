part of 'home_cubit.dart';

class HomeState {
  const HomeState({
    this.getPlacesStatus = GetPlacesStatus.initial,
    this.places = const [],
    this.errorMessage,
    this.currentPlace,
    this.destinationPlace,
  });

  final GetPlacesStatus getPlacesStatus;
  final List<PlaceReponse> places;
  final String? errorMessage;
  final PlaceReponse? currentPlace;
  final PlaceReponse? destinationPlace;

  HomeState copyWith({
    GetPlacesStatus? getPlacesStatus,
    List<PlaceReponse>? places,
    String? errorMessage,
    PlaceReponse? currentPlace,
    PlaceReponse? destinationPlace,
  }) {
    return HomeState(
      getPlacesStatus: getPlacesStatus ?? this.getPlacesStatus,
      places: places ?? this.places,
      errorMessage: errorMessage ?? this.errorMessage,
      currentPlace: currentPlace ?? this.currentPlace,
      destinationPlace: destinationPlace ?? this.destinationPlace,
    );
  }
}
