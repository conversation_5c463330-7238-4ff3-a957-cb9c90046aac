import 'dart:async';

import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/core/widgets/app_input.dart';
import 'package:base_app/features/home/<USER>/home_cubit.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'build_item_search_place_widget.dart';

class BuildSearchPlaceWidget extends StatefulWidget {
  final String title;
  final Function(PlaceReponse)? onTap;
  const BuildSearchPlaceWidget({
    super.key,
    required this.title,
    this.onTap,
  });

  @override
  State<BuildSearchPlaceWidget> createState() => _BuildSearchPlaceWidgetState();
}

class _BuildSearchPlaceWidgetState extends State<BuildSearchPlaceWidget> {
  TextEditingController searchCtrl = TextEditingController();
  Timer? _debounce;
  HomeCubit homeCubit = getIt<HomeCubit>();

  FocusNode searchFocusNode = FocusNode();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      bloc: homeCubit,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            context.appBarHeight.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: AppTextStyle.medium(20),
                ),
                AppAnimatedButton(
                  onTap: () => SafeNavigationUtil.pop(context),
                  child: const Icon(Icons.close),
                )
              ],
            ),
            AppTextFormField(
              focusNode: searchFocusNode,
              controller: searchCtrl,
              hintText: AppString.searchPlace,
              onChanged: (p0) {
                if (_debounce?.isActive ?? false) _debounce!.cancel();
                _debounce = Timer(const Duration(milliseconds: 700), () {
                  if (p0.isNotEmpty && p0.trim().isNotEmpty) {
                    homeCubit.getPlaces(place: p0);
                  }
                });
              },
            ),
            context.verticalSpaceHigh,
            if (state.getPlacesStatus == GetPlacesStatus.loading) ...[
              context.verticalSpaceHigh,
              const Center(
                child: CircularProgressIndicator(
                  color: AppColors.honoluluBlue,
                ),
              )
            ] else ...[
              if (state.getPlacesStatus == GetPlacesStatus.success) ...[
                context.verticalSpaceHigh,
                Text(AppString.listSearchPlace, style: AppTextStyle.medium()),
                context.verticalSpaceHigh,
                if (searchCtrl.text != '')
                  Expanded(
                    child: ListView.separated(
                      itemCount: state.places.length,
                      separatorBuilder: (_, __) => context.verticalSpaceHigh,
                      itemBuilder: (context, index) {
                        final place = state.places[index];

                        return BuildItemSearchPlaceWidget(
                          callBack: (place) async {
                            // Lấy danh sách history hiện tại
                            final currentHistory =
                                await getIt<ILocalStorageService>()
                                    .getPlaceSelected();

                            // Tạo danh sách mới với địa điểm được chọn ở đầu
                            List<PlaceReponse> updatedHistory = [place];

                            // Thêm các địa điểm khác từ history (loại bỏ trùng lặp)
                            for (final historyPlace in currentHistory) {
                              if (historyPlace.placeId != place.placeId) {
                                updatedHistory.add(historyPlace);
                              }
                            }

                            // Giới hạn số lượng history (tối đa 10 địa điểm)
                            if (updatedHistory.length > 10) {
                              updatedHistory = updatedHistory.take(10).toList();
                            }

                            // Lưu lại history đã cập nhật
                            await getIt<ILocalStorageService>()
                                .savePlaceSelected(updatedHistory);

                            widget.onTap?.call(place);
                          },
                          place: place,
                        );
                      },
                    ),
                  ),
              ],
            ],
          ],
        );
      },
    );
  }
}
