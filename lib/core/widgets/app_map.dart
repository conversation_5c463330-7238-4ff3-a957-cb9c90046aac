import 'dart:convert';

import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:flutter/material.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class AppMapbox extends StatefulWidget {
  const AppMapbox({super.key});

  @override
  State<AppMapbox> createState() => _AppMapboxState();
}

class _AppMapboxState extends State<AppMapbox>
    with AutomaticKeepAliveClientMixin {
  MapboxMap? mapboxMap;

  final centerPosition = Position(106.6297, 10.8231);
  final buggyModelPosition = Position(106.6297, 10.8231);
  final carModelPosition = Position(106.6297, 10.8231);

  @override
  bool get wantKeepAlive => true;

  Future<void> _onMapCreated(MapboxMap map) async {
    debugPrint('=== onMapCreated called ===');
    mapboxMap = map;

    try {
      Future.wait([
        mapboxMap!.logo.updateSettings(LogoSettings(enabled: false)),
        mapboxMap!.attribution
            .updateSettings(AttributionSettings(enabled: false)),
        mapboxMap!.scaleBar.updateSettings(ScaleBarSettings(enabled: false)),
        mapboxMap!.compass.updateSettings(CompassSettings(enabled: false)),
      ]);
    } catch (e, stackTrace) {
      debugPrint('Error: $e');
      debugPrint('Stack: $stackTrace');
    }
  }

  void _onStyleLoaded(StyleLoadedEventData data) async {
    addModelLayer();
  }

  void addModelLayer() async {
    if (mapboxMap == null) {
      throw Exception("MapboxMap is not ready yet");
    }

    final buggyModelId = "model-buggy-id";
    final buggyModelUri =
        "https://github.com/KhronosGroup/glTF-Sample-Models/raw/d7a3cc8e51d7c573771ae77a57f16b0662a905c6/2.0/Buggy/glTF/Buggy.gltf";
    debugPrint('buggyModelUri => $buggyModelUri');
    await mapboxMap?.style.addStyleModel(buggyModelId, buggyModelUri);

    var buggyModelLocation = Point(coordinates: buggyModelPosition);

    await mapboxMap?.style.addSource(GeoJsonSource(
        id: "buggySourceId", data: json.encode(buggyModelLocation)));

    var buggyModelLayer = ModelLayer(
      id: "modelLayer-buggy",
      sourceId: "buggySourceId",
    );
    buggyModelLayer.modelId = buggyModelId;
    buggyModelLayer.modelScale = [0.25, 0.25, 0.25];
    buggyModelLayer.modelRotation = [0, 0, 90];
    buggyModelLayer.modelType = ModelType.COMMON_3D;
    mapboxMap?.style.addLayer(buggyModelLayer);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        MapWidget(
          key: const ValueKey<String>('mapWidget'),
          mapOptions: MapOptions(
            pixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          cameraOptions: CameraOptions(
            center: Point(coordinates: centerPosition),
            zoom: 17,
            bearing: 15,
            pitch: 55,
          ),
          onMapCreated: _onMapCreated,
          onTapListener: _onMapTap,
          onStyleLoadedListener: _onStyleLoaded,
        ),
        Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          right: 16,
          child: AppAnimatedButton(
            onTap: () {
              SafeNavigationUtil.pop(context);
            },
            child: Container(
              padding: context.paddingNormal,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: context.borderRadiusMedium,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onMapTap(MapContentGestureContext context) {
    final coordinate = context.point;
    debugPrint(
        'Map tapped at: ${coordinate.coordinates.lat}, ${coordinate.coordinates.lng}');
  }
}
