class ApiConstants {
  static String baseUrlDev = "http://45.76.152.104:8080/api/v1/";
  static String baseUrlProd = "https://api.shinex.io.vn/api/v1/";
  static const int connectTimeout = 60000; // 60 seconds
  static const int receiveTimeout = 60000; // 60 seconds
  static const int codeSuccess = 200;
  static const int codeAuthenticationFailed = 401;
  static const int codeServerError = 500;
  static const int codeForbidden = 403;

  static const String loginMobile = 'auth/login-app-mobile';
  static const String checkAccountExist = 'auth/is-username-exist';
  static const String verifyCapcha = 'auth/verify-capcha';
  static const String getDecodeKey = 'master-data/get-decode-key';
  static const String queryParamUsername = 'username';
  static const String booking = 'carpool/order/booking';
  static const String verifyOTP = "auth/verify-otp";
  static const String getPlaces = "place/auto-complete";
  static const String customerRegister = "auth/customer-register";
}
