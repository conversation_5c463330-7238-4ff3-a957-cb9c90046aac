enum Environment {
  dev('Dev'),
  prod('Prod');

  const Environment(this.value);
  final String value;
}

enum DecodeKeyStatus { initial, loading, success, failure }

enum ValidationType {
  email,
  phone,
  password,
  confirmPassword,
  point,
  user,
  startPoint,
  endPoint,
  seat,
  fullName,
}

enum BookingStatus { initial, loading, success, failure }

enum RecentPlaceType { place, recent }

enum OtpStatus { initial, loading, success, failure }

enum VerifyOTPStatus { initial, loading, success, failure }

enum ResendOTPStatus { initial, loading, success, failure }

enum GetPlacesStatus { initial, loading, success, failure }

enum SignupStatus { initial, loading, success, failure }
